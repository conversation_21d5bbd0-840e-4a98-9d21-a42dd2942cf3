import javax.microedition.lcdui.Image;
import javax.microedition.media.Player;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Demo class để sử dụng ResourceDecoder
 * V<PERSON> dụ cách giải mã các tài nguyên từ Miami Nights game
 */
public class ResourceDecoderDemo {
    
    // Default color palettes từ game
    private static final int[] DEFAULT_PALETTE = {
        0xFF000000, 0xFFFFFFFF, 0xFF000000, 0xFFFF6565, 
        0xFFFFFF65, 0xFF0090FF, 0xFFF000F0, 0xFFFFFF00
    };
    
    private static final int[] SKIN_PALETTE = {
        0xFFFFE4C4, 0xFFDEB887, 0xFFD2B48C, 0xFFBC9A6A,
        0xFFA0522D, 0xFF8B4513, 0xFF654321, 0xFF3C1810
    };
    
    public static void main(String[] args) {
        System.out.println("=== Miami Nights Resource Decoder Demo ===");
        
        // Demo 1: Gi<PERSON>i mã pack files
        demoPackFileDecoding();
        
        // Demo 2: <PERSON><PERSON><PERSON><PERSON> mã sprites
        demoSpriteDecoding();
        
        // Demo 3: Giải mã audio
        demoAudioDecoding();
        
        // Demo 4: Giải mã IGP files
        demoIGPDecoding();
        
        // Demo 5: Batch extraction
        demoBatchExtraction();
    }
    
    /**
     * Demo giải mã pack files
     */
    public static void demoPackFileDecoding() {
        System.out.println("\n--- Demo Pack File Decoding ---");
        
        // Mở pack file chính
        if (ResourceDecoder.openPackFile("/0")) {
            System.out.println("✓ Opened main pack file successfully");
            
            // Giải mã splash screen
            Image splashImage = ResourceDecoder.decodeImage(0);
            if (splashImage != null) {
                System.out.println("✓ Decoded splash image: " + 
                    splashImage.getWidth() + "x" + splashImage.getHeight());
            }
            
            // Giải mã game logo
            Image logoImage = ResourceDecoder.decodeImage(1);
            if (logoImage != null) {
                System.out.println("✓ Decoded logo image: " + 
                    logoImage.getWidth() + "x" + logoImage.getHeight());
            }
            
            ResourceDecoder.closeCurrentPack();
        } else {
            System.out.println("✗ Failed to open pack file");
        }
    }
    
    /**
     * Demo giải mã sprites
     */
    public static void demoSpriteDecoding() {
        System.out.println("\n--- Demo Sprite Decoding ---");
        
        if (ResourceDecoder.openPackFile("/1")) {
            System.out.println("✓ Opened sprite pack file");
            
            // Giải mã character sprites
            for (int i = 0; i < 5; i++) {
                byte[] spriteData = ResourceDecoder.extractResource(i);
                if (spriteData != null) {
                    // Giả sử sprite 32x32 pixels
                    Image sprite = ResourceDecoder.decodeSprite(spriteData, 32, 32, SKIN_PALETTE);
                    if (sprite != null) {
                        System.out.println("✓ Decoded character sprite " + i + ": " +
                            sprite.getWidth() + "x" + sprite.getHeight());
                    }
                }
            }
            
            ResourceDecoder.closeCurrentPack();
        }
    }
    
    /**
     * Demo giải mã audio
     */
    public static void demoAudioDecoding() {
        System.out.println("\n--- Demo Audio Decoding ---");
        
        if (ResourceDecoder.openPackFile("/18")) {
            System.out.println("✓ Opened audio pack file");
            
            // Giải mã background music (MIDI)
            Player bgMusic = ResourceDecoder.decodeAudio(0, 2); // MIDI format
            if (bgMusic != null) {
                System.out.println("✓ Decoded background music (MIDI)");
                try {
                    bgMusic.close();
                } catch (Exception e) {}
            }
            
            // Giải mã sound effects (WAV)
            Player sfx = ResourceDecoder.decodeAudio(1, 1); // WAV format
            if (sfx != null) {
                System.out.println("✓ Decoded sound effect (WAV)");
                try {
                    sfx.close();
                } catch (Exception e) {}
            }
            
            ResourceDecoder.closeCurrentPack();
        }
    }
    
    /**
     * Demo giải mã IGP files
     */
    public static void demoIGPDecoding() {
        System.out.println("\n--- Demo IGP File Decoding ---");
        
        ResourceDecoder.IGPData igpData = ResourceDecoder.decodeIGP("/dataIGP");
        if (igpData != null) {
            System.out.println("✓ Opened IGP file with " + igpData.entryCount + " entries");
            
            // Trích xuất một số entries
            for (int i = 0; i < Math.min(5, igpData.entryCount - 1); i++) {
                byte[] entryData = ResourceDecoder.extractIGPEntry(igpData, i);
                if (entryData != null) {
                    System.out.println("✓ Extracted IGP entry " + i + ": " + entryData.length + " bytes");
                    
                    // Thử giải mã như image
                    try {
                        Image img = Image.createImage(entryData, 0, entryData.length);
                        System.out.println("  → Decoded as image: " + img.getWidth() + "x" + img.getHeight());
                    } catch (Exception e) {
                        System.out.println("  → Not an image format");
                    }
                }
            }
            
            igpData.close();
        }
    }
    
    /**
     * Demo batch extraction
     */
    public static void demoBatchExtraction() {
        System.out.println("\n--- Demo Batch Extraction ---");
        
        String[] packFiles = {"/0", "/1", "/3", "/8", "/9", "/18", "/19", "/20"};
        
        for (String packFile : packFiles) {
            if (ResourceDecoder.openPackFile(packFile)) {
                System.out.println("✓ Processing pack: " + packFile);
                
                // Trích xuất tất cả resources
                int resourceCount = 0;
                int imageCount = 0;
                int audioCount = 0;
                
                for (int i = 0; i < 50; i++) { // Giới hạn 50 resources per pack
                    byte[] data = ResourceDecoder.extractResource(i);
                    if (data == null) break;
                    
                    resourceCount++;
                    
                    // Thử decode như image
                    try {
                        Image img = ResourceDecoder.decodeImage(i);
                        if (img != null) {
                            imageCount++;
                            saveResourceToFile(data, packFile + "_img_" + i + ".png");
                        }
                    } catch (Exception e) {
                        // Thử decode như audio
                        Player audio = ResourceDecoder.decodeAudio(i, 1);
                        if (audio != null) {
                            audioCount++;
                            saveResourceToFile(data, packFile + "_audio_" + i + ".wav");
                            try { audio.close(); } catch (Exception ex) {}
                        } else {
                            // Save as binary data
                            saveResourceToFile(data, packFile + "_data_" + i + ".bin");
                        }
                    }
                }
                
                System.out.println("  → Extracted " + resourceCount + " resources (" + 
                    imageCount + " images, " + audioCount + " audio files)");
                
                ResourceDecoder.closeCurrentPack();
            }
        }
    }
    
    /**
     * Lưu resource ra file
     */
    private static void saveResourceToFile(byte[] data, String filename) {
        try {
            // Tạo thư mục extracted nếu chưa có
            java.io.File dir = new java.io.File("extracted");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            FileOutputStream fos = new FileOutputStream("extracted/" + filename.replace("/", "_"));
            fos.write(data);
            fos.close();
        } catch (IOException e) {
            System.err.println("Failed to save " + filename + ": " + e.getMessage());
        }
    }
    
    /**
     * Utility method để hiển thị hex dump
     */
    public static void printHexDump(byte[] data, int maxBytes) {
        System.out.println("Hex dump (first " + Math.min(maxBytes, data.length) + " bytes):");
        
        for (int i = 0; i < Math.min(maxBytes, data.length); i += 16) {
            System.out.printf("%04X: ", i);
            
            // Hex values
            for (int j = 0; j < 16 && i + j < data.length && i + j < maxBytes; j++) {
                System.out.printf("%02X ", data[i + j] & 0xFF);
            }
            
            // ASCII representation
            System.out.print(" |");
            for (int j = 0; j < 16 && i + j < data.length && i + j < maxBytes; j++) {
                char c = (char)(data[i + j] & 0xFF);
                System.out.print(Character.isISOControl(c) ? '.' : c);
            }
            System.out.println("|");
        }
    }
    
    /**
     * Phân tích header của resource
     */
    public static void analyzeResourceHeader(byte[] data) {
        if (data == null || data.length < 8) {
            System.out.println("Data too small for analysis");
            return;
        }
        
        System.out.println("Resource Analysis:");
        System.out.println("Size: " + data.length + " bytes");
        
        // Check for common file signatures
        if (data.length >= 4) {
            int signature = (data[0] & 0xFF) | ((data[1] & 0xFF) << 8) | 
                           ((data[2] & 0xFF) << 16) | ((data[3] & 0xFF) << 24);
            
            if ((data[0] & 0xFF) == 0x89 && (data[1] & 0xFF) == 0x50 && 
                (data[2] & 0xFF) == 0x4E && (data[3] & 0xFF) == 0x47) {
                System.out.println("Format: PNG Image");
            } else if ((data[0] & 0xFF) == 0xFF && (data[1] & 0xFF) == 0xD8) {
                System.out.println("Format: JPEG Image");
            } else if (data[0] == 'R' && data[1] == 'I' && data[2] == 'F' && data[3] == 'F') {
                System.out.println("Format: WAV Audio");
            } else if (data[0] == 'M' && data[1] == 'T' && data[2] == 'h' && data[3] == 'd') {
                System.out.println("Format: MIDI Audio");
            } else {
                System.out.println("Format: Unknown/Custom (signature: 0x" + 
                    Integer.toHexString(signature).toUpperCase() + ")");
            }
        }
        
        printHexDump(data, 64);
    }
}
